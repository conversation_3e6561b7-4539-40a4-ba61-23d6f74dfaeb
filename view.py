import os
import sqlite3
import sys
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, WebDriverException
from instagrapi import Client
import dotenv
import keyboard

# Load environment variables
dotenv.load_dotenv()


class InstagramBrowser:
    def __init__(self):
        self.cl = Client()
        self.driver = None
        self.users = []
        self.current_index = 0
        self.conn = sqlite3.connect('instagram_data.db')
        self.setup_database()
        self.setup_instagram_client()
        self.load_users()

    def setup_database(self):
        """Setup database tables"""
        cursor = self.conn.cursor()

        # Create removed table if it doesn't exist
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS removed (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER,
            username TEXT,
            removed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            action_type TEXT DEFAULT 'unfollowed'
        )
        ''')

        self.conn.commit()
        print("Database setup complete")

    def setup_instagram_client(self):
        """Setup Instagram API client"""
        try:
            self.cl.login(os.getenv("UNAME"), os.getenv("PASSWD"))
            print("Instagram client logged in successfully")
        except Exception as e:
            print(f"Failed to login to Instagram: {e}")
            sys.exit(1)

    def setup_selenium(self):
        """Setup Selenium WebDriver"""
        chrome_options = Options()
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument(
            "--disable-blink-features=AutomationControlled")
        chrome_options.add_experimental_option(
            "excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)

        try:
            self.driver = webdriver.Chrome(options=chrome_options)
            self.driver.execute_script(
                "Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            print("Selenium WebDriver setup complete")
        except Exception as e:
            print(f"Failed to setup Selenium: {e}")
            sys.exit(1)

    def load_users(self):
        """Load users from database"""
        cursor = self.conn.cursor()
        cursor.execute(
            "SELECT user_id, username FROM users WHERE following = 1")
        self.users = cursor.fetchall()
        print(f"Loaded {len(self.users)} users to browse")

    def navigate_to_user(self, username):
        """Navigate to Instagram user page"""
        url = f"https://www.instagram.com/{username}/"
        try:
            self.driver.get(url)
            # Wait for page to load
            WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.TAG_NAME, "main"))
            )
            return True
        except TimeoutException:
            print(f"Timeout loading page for {username}")
            return False
        except WebDriverException as e:
            print(f"Error navigating to {username}: {e}")
            return False

    def display_current_user(self):
        """Display current user information"""
        if not self.users:
            print("No users to display")
            return

        if self.current_index >= len(self.users):
            self.current_index = len(self.users) - 1
        elif self.current_index < 0:
            self.current_index = 0

        user_id, username = self.users[self.current_index]
        print(f"\n{'='*50}")
        print(f"User {self.current_index + 1} of {len(self.users)}: {username}")
        print(f"User ID: {user_id}")
        print(f"{'='*50}")

        return username

    def next_user(self):
        """Navigate to next user"""
        if self.current_index < len(self.users) - 1:
            self.current_index += 1
            username = self.display_current_user()
            if self.driver and username:
                self.navigate_to_user(username)
            return True
        else:
            print("Already at the last user")
            return False

    def previous_user(self):
        """Navigate to previous user"""
        if self.current_index > 0:
            self.current_index -= 1
            username = self.display_current_user()
            if self.driver and username:
                self.navigate_to_user(username)
            return True
        else:
            print("Already at the first user")
            return False

    def unfollow_current_user(self):
        """Unfollow current user and save to removed table"""
        if not self.users or self.current_index >= len(self.users):
            print("No user to unfollow")
            return False

        user_id, username = self.users[self.current_index]

        try:
            # Unfollow using instagrapi
            result = self.cl.user_unfollow(str(user_id))

            if result:
                # Save to removed table
                cursor = self.conn.cursor()
                cursor.execute('''
                INSERT INTO removed (user_id, username, action_type)
                VALUES (?, ?, ?)
                ''', (user_id, username, 'unfollowed'))

                # Update users table
                cursor.execute('''
                UPDATE users SET following = 0 WHERE user_id = ?
                ''', (user_id,))

                self.conn.commit()

                print(f"✓ Successfully unfollowed {username}")
                print(f"✓ User saved to removed table")

                # Remove from current users list
                self.users.pop(self.current_index)

                # Adjust current index if needed
                if self.current_index >= len(self.users) and len(self.users) > 0:
                    self.current_index = len(self.users) - 1

                return True
            else:
                print(f"✗ Failed to unfollow {username}")
                return False

        except Exception as e:
            print(f"✗ Error unfollowing {username}: {e}")
            return False

    def print_controls(self):
        """Print keyboard controls"""
        print("\n" + "="*60)
        print("KEYBOARD CONTROLS:")
        print("  ← Left Arrow:  Previous user")
        print("  → Right Arrow: Next user")
        print("  ↓ Down Arrow:  Unfollow current user and save to removed")
        print("  Q:             Quit application")
        print("  H:             Show this help")
        print("="*60)

    def handle_keyboard_input(self):
        """Handle keyboard input for navigation"""
        print("\nWaiting for keyboard input...")
        print("Press 'H' for help, 'Q' to quit")

        while True:
            try:
                # Get keyboard input
                event = keyboard.read_event()

                if event.event_type == keyboard.KEY_DOWN:
                    key = event.name

                    if key == 'left':
                        print("\n← Previous user")
                        self.previous_user()

                    elif key == 'right':
                        print("\n→ Next user")
                        self.next_user()

                    elif key == 'down':
                        print("\n↓ Unfollowing current user...")
                        success = self.unfollow_current_user()
                        if success and len(self.users) > 0:
                            # Show next user after unfollowing
                            username = self.display_current_user()
                            if self.driver and username:
                                self.navigate_to_user(username)
                        elif len(self.users) == 0:
                            print("\n🎉 All users processed!")
                            break

                    elif key == 'q':
                        print("\nQuitting application...")
                        break

                    elif key == 'h':
                        self.print_controls()

            except KeyboardInterrupt:
                print("\nApplication interrupted by user")
                break
            except Exception as e:
                print(f"Error handling keyboard input: {e}")
                continue

    def start_browser(self):
        """Start the Instagram browser"""
        if not self.users:
            print(
                "No users to browse. Please run getfollow.py first to populate the database.")
            return

        print("Starting Instagram Browser...")
        self.setup_selenium()

        # Display first user
        username = self.display_current_user()
        if username:
            self.navigate_to_user(username)

        self.print_controls()

        # Start keyboard input handling
        self.handle_keyboard_input()

        # Cleanup
        self.cleanup()

    def cleanup(self):
        """Cleanup resources"""
        if self.driver:
            self.driver.quit()
            print("Browser closed")

        if self.conn:
            self.conn.close()
            print("Database connection closed")

        print("Cleanup complete")


def main():
    """Main function to run the Instagram browser"""
    try:
        browser = InstagramBrowser()
        browser.start_browser()
    except KeyboardInterrupt:
        print("\nApplication interrupted")
    except Exception as e:
        print(f"Error: {e}")
    finally:
        print("Instagram Browser session ended")


if __name__ == "__main__":
    main()
