#!/usr/bin/env python3
"""
Test script to verify the Instagram browser setup
"""

import sqlite3
import os
from dotenv import load_dotenv

def test_database():
    """Test database connection and structure"""
    print("Testing database connection...")
    
    try:
        conn = sqlite3.connect('instagram_data.db')
        cursor = conn.cursor()
        
        # Check if users table exists
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='users'")
        users_table = cursor.fetchone()
        
        if users_table:
            print("✓ Users table exists")
            
            # Check user count
            cursor.execute("SELECT COUNT(*) FROM users WHERE following = 1")
            count = cursor.fetchone()[0]
            print(f"✓ Found {count} users you're following")
            
            if count == 0:
                print("⚠️  No following users found. Run 'python getfollow.py' first.")
        else:
            print("✗ Users table not found. Run 'python getfollow.py' first.")
        
        # Check if removed table exists (will be created by InstagramBrowser)
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='removed'")
        removed_table = cursor.fetchone()
        
        if removed_table:
            print("✓ Removed table exists")
        else:
            print("ℹ️  Removed table will be created when browser starts")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"✗ Database error: {e}")
        return False

def test_environment():
    """Test environment variables"""
    print("\nTesting environment variables...")
    
    load_dotenv()
    
    username = os.getenv("UNAME")
    password = os.getenv("PASSWD")
    
    if username and password:
        print("✓ Instagram credentials found in .env file")
        print(f"✓ Username: {username}")
        return True
    else:
        print("✗ Instagram credentials not found")
        print("Please create a .env file with UNAME and PASSWD")
        return False

def test_imports():
    """Test required imports"""
    print("\nTesting imports...")
    
    try:
        import selenium
        print("✓ Selenium imported")
    except ImportError:
        print("✗ Selenium not found. Run: pip install selenium")
        return False
    
    try:
        import keyboard
        print("✓ Keyboard imported")
    except ImportError:
        print("✗ Keyboard not found. Run: pip install keyboard")
        return False
    
    try:
        import instagrapi
        print("✓ Instagrapi imported")
    except ImportError:
        print("✗ Instagrapi not found. Run: pip install instagrapi")
        return False
    
    try:
        import dotenv
        print("✓ Python-dotenv imported")
    except ImportError:
        print("✗ Python-dotenv not found. Run: pip install python-dotenv")
        return False
    
    return True

def main():
    """Run all tests"""
    print("Instagram Browser Setup Test")
    print("=" * 40)
    
    all_passed = True
    
    all_passed &= test_imports()
    all_passed &= test_environment()
    all_passed &= test_database()
    
    print("\n" + "=" * 40)
    if all_passed:
        print("✓ All tests passed! You can run 'python view.py' to start the browser.")
    else:
        print("✗ Some tests failed. Please fix the issues above.")
    
    print("\nNote: Make sure Chrome browser is installed for Selenium to work.")

if __name__ == "__main__":
    main()
