# Instagram User Browser

A Selenium-based Instagram user browser that allows you to navigate through your following list and manage unfollowing actions with keyboard controls.

## Features

- **Navigation System**: Browse Instagram user pages using Selenium WebDriver
- **User List Management**: Load users from database and track progress
- **Keyboard Controls**: Navigate with arrow keys and perform actions
- **Terminal Logging**: Clear progress display and action feedback
- **Data Storage**: Track unfollowed users in separate database table

## Setup

1. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

2. Create a `.env` file with your Instagram credentials:
   ```
   UNAME=your_instagram_username
   PASSWD=your_instagram_password
   ```

3. First, run the data collection script to populate the database:
   ```bash
   python getfollow.py
   ```

4. Then run the browser:
   ```bash
   python view.py
   ```

## Keyboard Controls

- **← Left Arrow**: Navigate to previous user in the list
- **→ Right Arrow**: Navigate to next user in the list  
- **↓ Down Arrow**: Unfollow current user and save to 'removed' table
- **Q**: Quit application
- **H**: Show help

## Database Structure

### users table
- `user_id`: Instagram user ID
- `username`: Instagram username
- `following`: Boolean indicating if you're following them
- `follower`: Boolean indicating if they're following you

### removed table
- `id`: Auto-increment primary key
- `user_id`: Instagram user ID
- `username`: Instagram username
- `removed_at`: Timestamp when action was performed
- `action_type`: Type of action (default: 'unfollowed')

## Usage

1. The browser will display the current user in format: "User X of Y: {username}"
2. Use arrow keys to navigate through your following list
3. Press down arrow to unfollow the current user
4. Unfollowed users are automatically saved to the 'removed' table
5. The browser will automatically move to the next user after unfollowing

## Requirements

- Python 3.7+
- Chrome browser (for Selenium WebDriver)
- Instagram account credentials
- Active internet connection

## Notes

- Make sure Chrome browser is installed on your system
- The application requires administrator privileges on Windows for keyboard input detection
- Be mindful of Instagram's rate limits when unfollowing users
- The browser will automatically handle page loading and navigation delays
