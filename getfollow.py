from instagrapi import Client
import dotenv
import os
import selenium
import sqlite3

dotenv.load_dotenv()

cl = Client()
cl.login(os.getenv("UNAME"), os.getenv("PASSWD"))


print("Finish login")

following = cl.user_following(cl.user_id)
followers = cl.user_followers(cl.user_id)

# Create SQLite database and table
conn = sqlite3.connect('instagram_data.db')
cursor = conn.cursor()

# Create users table if it doesn't exist
cursor.execute('''
CREATE TABLE IF NOT EXISTS users (
    user_id INTEGER PRIMARY KEY,
    username TEXT,
    following B<PERSON><PERSON><PERSON><PERSON>,
    follower BOOLEAN
)
''')

# Insert data into the database
for user_id, user_info in following.items():
    cursor.execute('''
    INSERT OR REPLACE INTO users (user_id, username, following, follower)
    VALUES (?, ?, ?, ?)
    ''', (int(user_id), user_info.username, True, False))

for user_id, user_info in followers.items():
    # Check if user already exists in the database
    cursor.execute('SELECT * FROM users WHERE user_id = ?', (int(user_id),))
    existing_user = cursor.fetchone()

    if existing_user:
        # Update existing user
        cursor.execute('''
        UPDATE users SET follower = ? WHERE user_id = ?
        ''', (True, int(user_id)))
    else:
        # Insert new user
        cursor.execute('''
        INSERT INTO users (user_id, username, following, follower)
        VALUES (?, ?, ?, ?)
        ''', (int(user_id), user_info.username, False, True))

# Commit changes and close connection
conn.commit()
conn.close()

print("Data successfully stored in database")
